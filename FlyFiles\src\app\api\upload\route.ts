import { NextRequest, NextResponse } from 'next/server'
import { ApiResponse } from '@/app/lib/types'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const file = formData.get('file') as File

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' } as ApiResponse,
        { status: 400 }
      )
    }

    // Get Terabox credentials from environment
    const ndus = process.env.ndus
    const ndut_fmt = process.env.ndut_fmt
    const jsToken = process.env.JS_TOKEN
    const browserId = process.env.browserId

    if (!ndus || !ndut_fmt || !jsToken || !browserId) {
      return NextResponse.json(
        { success: false, error: 'Terabox credentials not configured' } as ApiResponse,
        { status: 500 }
      )
    }

    // Convert file to buffer
    const buffer = await file.arrayBuffer()
    const fileBuffer = Buffer.from(buffer)

    // Upload to Terabox
    const uploadResult = await uploadToTerabox(fileBuffer, file.name, {
      ndus,
      ndut_fmt,
      jsToken,
      browserId
    })

    if (!uploadResult.success) {
      return NextResponse.json(
        { success: false, error: uploadResult.error } as ApiResponse,
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: {
        filename: file.name,
        size: file.size,
        teraboxUrl: uploadResult.url,
        uploadedAt: new Date().toISOString()
      }
    } as ApiResponse)

  } catch (error) {
    console.error('Upload error:', error)
    return NextResponse.json(
      { success: false, error: 'Upload failed' } as ApiResponse,
      { status: 500 }
    )
  }
}

interface TeraboxCredentials {
  ndus: string
  ndut_fmt: string
  jsToken: string
  browserId: string
}

interface TeraboxUploadResult {
  success: boolean
  url?: string
  error?: string
}

async function uploadToTerabox(
  fileBuffer: Buffer,
  filename: string,
  credentials: TeraboxCredentials
): Promise<TeraboxUploadResult> {
  try {
    // Step 1: Get upload URL and parameters
    const uploadInfoResponse = await fetch('https://www.terabox.com/api/upload', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Cookie': `ndus=${credentials.ndus}; ndut_fmt=${credentials.ndut_fmt}; browserid=${credentials.browserId}`,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      body: new URLSearchParams({
        jsToken: credentials.jsToken,
        target_path: '/',
        upload_id: Date.now().toString(),
        app_id: '250528'
      })
    })

    if (!uploadInfoResponse.ok) {
      throw new Error(`Failed to get upload info: ${uploadInfoResponse.status}`)
    }

    const uploadInfo = await uploadInfoResponse.json()

    if (uploadInfo.errno !== 0) {
      throw new Error(`Terabox API error: ${uploadInfo.errmsg || 'Unknown error'}`)
    }

    // Step 2: Upload the file
    const uploadFormData = new FormData()
    uploadFormData.append('file', new Blob([fileBuffer]), filename)
    uploadFormData.append('path', '/')
    uploadFormData.append('upload_id', uploadInfo.upload_id)
    uploadFormData.append('jsToken', credentials.jsToken)

    const uploadResponse = await fetch(uploadInfo.upload_url, {
      method: 'POST',
      headers: {
        'Cookie': `ndus=${credentials.ndus}; ndut_fmt=${credentials.ndut_fmt}; browserid=${credentials.browserId}`,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      },
      body: uploadFormData
    })

    if (!uploadResponse.ok) {
      throw new Error(`Upload failed: ${uploadResponse.status}`)
    }

    const uploadResult = await uploadResponse.json()

    if (uploadResult.errno !== 0) {
      throw new Error(`Upload error: ${uploadResult.errmsg || 'Unknown error'}`)
    }

    return {
      success: true,
      url: uploadResult.info?.[0]?.path || `https://www.terabox.com/s/${uploadResult.info?.[0]?.fs_id}`
    }

  } catch (error) {
    console.error('Terabox upload error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown upload error'
    }
  }
}