# MongoDB Configuration (used by NextAuth.js and the application)
MONGODB_URI=mongodb+srv://Myckas:<EMAIL>/flyfiles?retryWrites=true&w=majority&appName=Cluster0

# NextAuth.js Configuration
NEXTAUTH_URL=https://flyfiles.mcdevhub.dk  # Update this for production
NEXTAUTH_SECRET=9ca30f4947c68865a7821c79d9783f181ce39e18bb2c0df5ba300c00738b49e2

# Development URL (for local testing)
# NEXTAUTH_URL=http://localhost:3001

# Legacy JWT Secret (still used by some admin routes)
JWT_SECRET=9ca30f4947c68865a7821c79d9783f181ce39e18bb2c0df5ba300c00738b49e2

# Admin API Secret for verification endpoints
ADMIN_API_SECRET=123456789

# Cloudinary configuration (FOR FUTURE USE)
CLOUDINARY_CLOUD_NAME=dwqxk2tip
CLOUDINARY_API_KEY=468174528553651
CLOUDINARY_API_SECRET=rZk3RfvtL1PxPUFT-HplZOKMVTo
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=dwqxk2tip



STRIPE_SECRET_KEY=sk_test_51OlcexGunuvAGRgKFWJL30XibiiWgJ5uPh6SjaRkbwdrSQc5x45LLLW6xUdRzYnBeCyGgLiX3YV74i9uxSIiioxf009GPM9AXe
STRIPE_PUBLISHABLE_KEY=pk_test_51OlcexGunuvAGRgKWPhMq7kylg8AysbnC727bNPKAXBCPUNqWiaRi06UGlItG3Ulba3zOmGK8nb7FRAHqNKHQHY6004eSBlM86
NEXT_PUBLIC_BASE_URL=https://flyfiles.mcdevhub.dk  # Update this for production FOR FUTURE USE



browserId=JAnYEKRngTCHfR3v5boNwqm30wDUpC4LlBpZjO49uzBMbGBBkArcUS4zAXM
JS_TOKEN=5682116EB63825A2FA70BFDAF6E72C4453AD07960A22839DBADA6978295B62DA0CB59BF5636EB1AD2E98729F36B7498732F77B7211E1886BDDEE5E2D18BA1591
ndus=Yu1xGrEteHuiET6OlKfLkPLoYLE9TkER3F0_J8RX
ndut_fmt=E45F16A5B2E86D59307725B3DC62D7E3A9F89877F362CA76138FFAED06FF5309