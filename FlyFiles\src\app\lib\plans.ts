// Plan configurations - separated from MongoDB to avoid client-side bundling issues
export const PLAN_CONFIGS = {
  guest: {
    name: '<PERSON>æst',
    uploadLimit: {
      amount: 250 * 1024 * 1024, // 250MB
      period: 'session' as const
    },
    fileExpiry: 7, // dage
    downloadLimits: {
      configurable: false,
      unlimited: true
    },
    features: [
      '250MB pr. session',
      '7 dages filudløb',
      'Ubegrænsede downloads',
      'Ingen konto nødvendig'
    ]
  },
  free: {
    name: '<PERSON><PERSON><PERSON>',
    uploadLimit: {
      amount: 15 * 1024 * 1024 * 1024, // 15GB
      period: 'month' as const
    },
    fileExpiry: 10, // dage
    downloadLimits: {
      configurable: true,
      unlimited: false
    },
    features: [
      '15GB monthly upload',
      '10-day file expiry',
      'Configurable download limits',
      'Google login required'
    ]
  },
  upgrade1: {
    name: 'Upgrade 1',
    uploadLimit: {
      amount: 15 * 1024 * 1024 * 1024, // 15GB
      period: 'week' as const
    },
    fileExpiry: 14, // days
    downloadLimits: {
      configurable: true,
      unlimited: false
    },
    features: [
      '15GB weekly upload',
      '14-day file expiry',
      'Download statistics',
      'All Free features'
    ],
    price: {
      amount: 5,
      period: 'month' as const
    }
  },
  upgrade2: {
    name: 'Upgrade 2',
    uploadLimit: {
      amount: 50 * 1024 * 1024 * 1024, // 50GB
      period: 'week' as const
    },
    fileExpiry: 30, // days
    downloadLimits: {
      configurable: true,
      unlimited: true
    },
    features: [
      '50GB weekly upload',
      '30-day file expiry',
      'Unlimited downloads',
      'Advanced analytics',
      'All previous features'
    ],
    price: {
      amount: 25,
      period: 'month' as const
    }
  }
} as const;
