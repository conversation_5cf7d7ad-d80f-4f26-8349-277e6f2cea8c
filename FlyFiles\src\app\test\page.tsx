'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/app/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card'
import { Input } from '@/app/components/ui/input'
import { Label } from '@/app/components/ui/label'
import { Alert, AlertDescription } from '@/app/components/ui/alert'
import { Upload, FileText, CheckCircle, XCircle, Loader2 } from 'lucide-react'

interface UploadResult {
  success: boolean
  data?: {
    filename: string
    size: number
    teraboxUrl: string
    uploadedAt: string
  }
  error?: string
}

export default function TestPage() {
  const [file, setFile] = useState<File | null>(null)
  const [uploading, setUploading] = useState(false)
  const [result, setResult] = useState<UploadResult | null>(null)

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0]
    if (selectedFile) {
      setFile(selectedFile)
      setResult(null) // Clear previous results
    }
  }

  const handleUpload = async () => {
    if (!file) return

    setUploading(true)
    setResult(null)

    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData
      })

      const result: UploadResult = await response.json()
      setResult(result)

    } catch (error) {
      setResult({
        success: false,
        error: 'Network error occurred'
      })
    } finally {
      setUploading(false)
    }
  }

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-2xl mx-auto pt-8">
        <Card className="shadow-lg">
          <CardHeader className="text-center">
            <CardTitle className="text-2xl font-bold text-gray-800 flex items-center justify-center gap-2">
              <Upload className="h-6 w-6" />
              Terabox Upload Test
            </CardTitle>
            <CardDescription>
              Test file upload to your Terabox cloud storage
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            {/* File Selection */}
            <div className="space-y-2">
              <Label htmlFor="file-upload" className="text-sm font-medium">
                Select File
              </Label>
              <Input
                id="file-upload"
                type="file"
                onChange={handleFileChange}
                className="cursor-pointer"
                accept="*/*"
              />
              {file && (
                <div className="flex items-center gap-2 text-sm text-gray-600 bg-gray-50 p-2 rounded">
                  <FileText className="h-4 w-4" />
                  <span>{file.name}</span>
                  <span className="text-gray-400">({formatFileSize(file.size)})</span>
                </div>
              )}
            </div>

            {/* Upload Button */}
            <Button
              onClick={handleUpload}
              disabled={!file || uploading}
              className="w-full"
              size="lg"
            >
              {uploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Upload to Terabox
                </>
              )}
            </Button>

            {/* Results */}
            {result && (
              <Alert className={result.success ? 'border-green-200 bg-green-50' : 'border-red-200 bg-red-50'}>
                <div className="flex items-start gap-2">
                  {result.success ? (
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                  ) : (
                    <XCircle className="h-5 w-5 text-red-600 mt-0.5" />
                  )}
                  <div className="flex-1">
                    <AlertDescription>
                      {result.success ? (
                        <div className="space-y-2">
                          <p className="font-medium text-green-800">Upload successful!</p>
                          <div className="text-sm text-green-700 space-y-1">
                            <p><strong>Filename:</strong> {result.data?.filename}</p>
                            <p><strong>Size:</strong> {result.data?.size ? formatFileSize(result.data.size) : 'Unknown'}</p>
                            <p><strong>Uploaded at:</strong> {result.data?.uploadedAt ? new Date(result.data.uploadedAt).toLocaleString() : 'Unknown'}</p>
                            {result.data?.teraboxUrl && (
                              <p>
                                <strong>Terabox URL:</strong>{' '}
                                <a 
                                  href={result.data.teraboxUrl} 
                                  target="_blank" 
                                  rel="noopener noreferrer"
                                  className="text-blue-600 hover:text-blue-800 underline break-all"
                                >
                                  {result.data.teraboxUrl}
                                </a>
                              </p>
                            )}
                          </div>
                        </div>
                      ) : (
                        <div>
                          <p className="font-medium text-red-800">Upload failed</p>
                          <p className="text-sm text-red-700 mt-1">{result.error}</p>
                        </div>
                      )}
                    </AlertDescription>
                  </div>
                </div>
              </Alert>
            )}

            {/* Instructions */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h3 className="font-medium text-blue-900 mb-2">Instructions:</h3>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Select any file you want to upload to Terabox</li>
                <li>• Click "Upload to Terabox" to start the upload</li>
                <li>• The file will be uploaded to your Terabox root directory</li>
                <li>• You'll see the result and Terabox URL if successful</li>
              </ul>
            </div>

            {/* Credentials Info */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
              <h3 className="font-medium text-gray-900 mb-2">Terabox Configuration:</h3>
              <p className="text-sm text-gray-600">
                This test uses your hardcoded Terabox credentials from the .env file:
              </p>
              <ul className="text-xs text-gray-500 mt-2 space-y-1">
                <li>• ndus: {process.env.NEXT_PUBLIC_TERABOX_CONFIGURED ? '✓ Configured' : '✗ Not configured'}</li>
                <li>• ndut_fmt: {process.env.NEXT_PUBLIC_TERABOX_CONFIGURED ? '✓ Configured' : '✗ Not configured'}</li>
                <li>• JS_TOKEN: {process.env.NEXT_PUBLIC_TERABOX_CONFIGURED ? '✓ Configured' : '✗ Not configured'}</li>
                <li>• browserId: {process.env.NEXT_PUBLIC_TERABOX_CONFIGURED ? '✓ Configured' : '✗ Not configured'}</li>
              </ul>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
